[package]
name = "deadlock-api-ingest"
version = "0.1.0"
description = "Deadlock API Ingest"
repository = "https://github.com/deadlock-api/deadlock-api-ingest"
license = "MIT"
edition = "2024"

[dependencies]
anyhow = "1.0.100"
tracing = "0.1.41"
reqwest = { version = "0.12.23", features = ["blocking", "json"] }
tracing-subscriber = { version = "0.3.20", features = ["std", "registry", "fmt", "env-filter"], default-features = false }
serde = { version = "1.0.226", features = ["derive"] }
memchr = "2.7.5"

[target.'cfg(target_os = "windows")'.dependencies]
pktmon = "0.6.2"

[target.'cfg(target_os = "linux")'.dependencies]
pcap = "2.3.0"

[profile.release]
strip = true
opt-level = "z"
lto = true
codegen-units = 1
panic = "abort"

[dev-dependencies]
rstest = "0.26.1"
